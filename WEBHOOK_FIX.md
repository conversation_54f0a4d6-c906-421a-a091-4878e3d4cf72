# Webhook Content-Type Fix

## Problem
The webhook endpoint was receiving 415 "Unsupported Media Type" errors because TradingView was sending requests without the `Content-Type: application/json` header, but <PERSON><PERSON><PERSON>'s `request.get_json()` requires this header.

## Solution
Modified the webhook endpoint to handle multiple content types:

### Changes Made

1. **Enhanced Webhook Parsing** (`app.py` lines 457-505):
   - Added flexible content-type detection
   - Handles JSON with correct content-type
   - Handles JSON with wrong/missing content-type
   - Handles key:value format (e.g., "coin:BTC action:BUY market_order:1")
   - Handles form data
   - Added comprehensive logging for debugging

2. **Added Debug Endpoint** (`app.py` lines 612-638):
   - New `/debug` endpoint to see exactly what TradingView is sending
   - Returns all request details: headers, content-type, raw data, etc.

3. **Added CORS Support**:
   - Added Flask-CORS dependency
   - Enabled CORS for all routes to handle cross-origin requests

4. **Updated Dependencies** (`requirements.txt`):
   - Added `Flask-CORS==5.0.0`

## Testing

### Local Testing
1. Run the Flask app locally:
   ```bash
   python app.py
   ```

2. Test different formats:
   ```bash
   python test_webhook.py
   ```

### Production Testing
1. Update the base_url in `test_webhook.py` to your Railway URL
2. Run the test script to verify all formats work

### Debug TradingView Requests
Use the new debug endpoint to see exactly what TradingView is sending:
```
POST https://your-railway-url.up.railway.app/debug
```

## Deployment Steps

1. **Commit and push changes**:
   ```bash
   git add .
   git commit -m "Fix webhook content-type handling"
   git push
   ```

2. **Railway will automatically redeploy** with the new changes

3. **Test the webhook** using the debug endpoint first:
   ```
   POST https://web-production-0efa7.up.railway.app/debug
   Headers: X-Webhook-Secret: itsMike818!
   Body: coin:BTC action:BUY market_order:1
   ```

4. **Update TradingView alerts** if needed based on debug results

## Supported Webhook Formats

The webhook now supports:

1. **JSON with correct content-type**:
   ```
   Content-Type: application/json
   {"coin": "BTC", "action": "BUY", "market_order": "1"}
   ```

2. **JSON with wrong content-type**:
   ```
   Content-Type: text/plain
   {"coin": "BTC", "action": "BUY", "market_order": "1"}
   ```

3. **Key:value format**:
   ```
   Content-Type: text/plain
   coin:BTC action:BUY market_order:1
   ```

4. **Form data**:
   ```
   Content-Type: application/x-www-form-urlencoded
   coin=BTC&action=BUY&market_order=1
   ```

## Next Steps

1. Deploy the changes to Railway
2. Test with the debug endpoint to see what TradingView actually sends
3. Verify webhook works with actual TradingView alerts
4. Monitor logs for any remaining issues

The webhook should now handle whatever format TradingView is sending!
