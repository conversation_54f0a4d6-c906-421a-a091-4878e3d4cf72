#!/usr/bin/env python3
"""
Test script to verify webhook endpoint handles different content types
"""

import requests
import json

# Test different webhook formats
def test_webhook_formats():
    base_url = "http://localhost:5000"  # Change to your Railway URL when testing deployed version
    
    test_cases = [
        {
            "name": "JSON with correct content-type",
            "url": f"{base_url}/webhook",
            "headers": {
                "Content-Type": "application/json",
                "X-Webhook-Secret": "testsecret"
            },
            "data": json.dumps({"coin": "BTC", "action": "BUY", "market_order": "1"})
        },
        {
            "name": "J<PERSON><PERSON> with wrong content-type",
            "url": f"{base_url}/webhook",
            "headers": {
                "Content-Type": "text/plain",
                "X-Webhook-Secret": "testsecret"
            },
            "data": json.dumps({"coin": "BTC", "action": "BUY", "market_order": "1"})
        },
        {
            "name": "Key:value format",
            "url": f"{base_url}/webhook",
            "headers": {
                "Content-Type": "text/plain",
                "X-Webhook-Secret": "testsecret"
            },
            "data": "coin:BTC action:BUY market_order:1"
        },
        {
            "name": "Form data",
            "url": f"{base_url}/webhook",
            "headers": {
                "Content-Type": "application/x-www-form-urlencoded",
                "X-Webhook-Secret": "testsecret"
            },
            "data": "coin=BTC&action=BUY&market_order=1"
        },
        {
            "name": "Debug endpoint test",
            "url": f"{base_url}/debug",
            "headers": {
                "Content-Type": "text/plain",
                "X-Webhook-Secret": "testsecret"
            },
            "data": "coin:BTC action:BUY market_order:1"
        }
    ]
    
    print("Testing webhook endpoint with different formats...\n")
    
    for test_case in test_cases:
        print(f"Testing: {test_case['name']}")
        print(f"URL: {test_case['url']}")
        print(f"Headers: {test_case['headers']}")
        print(f"Data: {test_case['data']}")
        
        try:
            response = requests.post(
                test_case['url'],
                headers=test_case['headers'],
                data=test_case['data'],
                timeout=10
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            
            if response.status_code == 200:
                print("✅ SUCCESS")
            else:
                print("❌ FAILED")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ REQUEST ERROR: {e}")
        
        print("-" * 80)
        print()

if __name__ == "__main__":
    test_webhook_formats()
